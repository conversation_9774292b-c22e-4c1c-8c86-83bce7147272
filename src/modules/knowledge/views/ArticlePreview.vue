<template>
  <iframe ref="iframe" frameborder="0" class="article-preview"></iframe>
</template>

<script lang="ts">
import { useUserStore } from '@/stores';

export default {
  name: 'ArticlePreview',
  props: {
    content: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: false,
      default: '',
    },
    subtitle: {
      type: String,
      required: false,
      default: '',
    },
    updated: {
      type: String,
      required: false,
      default: '',
    },
    visibility: {
      type: String,
      required: false,
      default: 'Public',
    },
    screensUrl: {
      type: String,
      required: false,
      default: 'https://f.goboomtown.com/screens',
    },
    articleTheme: {
      type: String,
      required: false,
      default: 'modern',
    },
    libraryCss: {
      type: String,
      required: false,
      default: '',
    },
    articleVisibility: {
      type: String,
      required: false,
      default: 'Public',
    },
  },
  mounted() {
    this.renderContent();
  },
  methods: {
    renderContent() {
      const iframe = this.$refs.iframe as HTMLIFrameElement;
      const doc = iframe.contentDocument || (iframe.contentWindow?.document);
      const baseUrl = useUserStore().screensUrl || this.screensUrl;

      const styles = `
      <style>
        ${this.libraryCss}
        @import url("//fonts.googleapis.com/css2?family=Inter");
        body {
            max-width: 1100px;
            width: 100%;
            margin: 0 auto !important;
            padding: 2rem 4rem 2rem 4rem !important;
            overflow-x: hidden;
            font-family: "Inter", Helvetica, sans-serif;
        }
        
        body > div:nth-of-type(2) {
            padding-bottom: 1rem;
        }

        .fa, .fas {
          font-family: "Font Awesome 5 Pro" !important;
        }
        
        .title-section {
          margin-bottom: 2rem;
        }
        
        .title-field {
          margin-bottom: 1rem;
        }
        
        .title-field h1 {
          margin: 0;
          font-size: 28px;
          color: var(--text-color-primary, #1e293b);
          line-height: 1.5;
          font-weight: 600;
        }
        
        .subtitle-field {
          margin-bottom: 1rem;
        }
        
        .subtitle-field h2 {
          margin: 0;
          font-size: 1.25rem;
          color: var(--text-color-secondary, #64748b);
          font-weight: normal;
        }
        
        .article-meta {
          display: flex;
          align-items: center;
          color: var(--text-color-secondary, #64748b);
          padding: 0 0 1rem 0;
          font-size: 0.875rem;
        }
        
        .visibility {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          margin-left: 0.5rem;
        }
        
        .visibility i {
          font-size: 0.875rem;
        }

        body, * {
          font-family: "Inter", Helvetica, sans-serif !important;
        }
      </style>
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/kb.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/header.css">
      <link rel="stylesheet" href="${baseUrl}/article/${this.articleTheme}/stylesheets/accessibility.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/foundation.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/all.min.css">
      <link rel="stylesheet" href="${baseUrl}/assets/stylesheets/codesnippets.css">
      <link rel="stylesheet" type="text/css" href="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/css/normalize.css">
      `;

      const scripts = `
      <script src="${baseUrl}/assets/javascripts/jquery/3.5.1/jquery-3.5.1.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/foundation.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/jquery/plugins/jquery.waitforimages.min.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/scrollnav-3.0.2/scrollnav.min.umd.js"><\/script>
      <script src="${baseUrl}/assets/javascripts/foundation-5.3.0.custom/js/vendor/modernizr.js"><\/script>
      <script src="${baseUrl}/article/${this.articleTheme}/javascripts/default.js"><\/script>
      <script src="${baseUrl}/article/shared/javascripts/kbSearch.js"><\/script>
      <!--<script src="${baseUrl}/article/shared/javascripts/kbArticle.js"><\/script>-->
      <script type="text/javascript">
        // front-end fixes to DOM for accessibility and other concerns
// shared for modern and default themes

'use strict';

(function(){
    const worker = new SharedWorker('/screens/article/shared/javascripts/timeoutWorker.js?v=1');
    const port = worker.port;
    let sessionTimer, timer, logoutModal;

    $(() => {
        // document onReady:
        const logoutButton = $('ul.user-controls button.logout');

        if(logoutButton.length){
            port.onmessage = e => {
                let data = e.data;

                if (data.action === 'showModal') {
                    // show timeout modal
                    showPopup();
                }
                else if (data.action === 'closeModal') {
                    if (sessionTimer && sessionTimer.dialog) {
                        sessionTimer.hide();
                        sessionTimer = null;
                    }
                }
                else if(data.action === 'logout'){
                    logoutUser();
                }
                else if(data.action === 'loginModal'){
                    sessionStorage.setItem("reloading", "true");
                    showLoginModal();
                }
                else if(data.action === 'loginUser'){
                    loginUsers();
                }
            };
            port.postMessage({action: 'start', idleTimeout: logoutButton.data('idleLogout')});

            document.addEventListener('click',function(e){
                port.postMessage({action: 'updateTimer'});
            });

            document.addEventListener('mousemove',function(e){
                port.postMessage({action: 'updateTimer'});
            });

            document.addEventListener('keydown',function(e){
                port.postMessage({action: 'updateTimer'});
            });
        }

        /*
         * ADA Image alt tag defaults
         */
        (($) => {

            // Most images in kb articles have alt text (either explicitly defined by the author,
            // or defaulting to alt="<filename>" or alt="") but some do not for unknown reasons.
            // This will set alt="" for the few that don't already have alt text.

            // TODO figure out the root cause of the images with missing alt text.

            $("article").find('img').each(function() {
                const el = this;
                if (! $(el)[0].hasAttribute("alt") ) {
                    $(el).attr("alt", '');
                }
            });
        })(jQuery);

        /*
         * ADA Video iframe titles defaults
         */
        (($) => {
            const articleTitle = $("main").find("h1.kb-title")[0] ? $("main").find("h1.kb-title")[0].innerText : 'Knowledge Base Video';
            const defaultVideoTitle = `Video for KB Article: ${articleTitle}`;
            let videoCount = 1,
                attributeToFix;

            $("span.fr-video").find("iframe,video").each(function() {
                const el = this;

                if (el.nodeName === 'IFRAME') {
                    attributeToFix = 'title';
                } else if (el.nodeName === 'VIDEO') {
                    attributeToFix = 'aria-label';
                } else {
                    return true; // next loop iteration if the nodeName is not one of two we expect
                }

                if ($(el)[0] && !$(el)[0].hasAttribute(attributeToFix) ) {
                    $(el).attr(attributeToFix, `${defaultVideoTitle} (${videoCount++})`);
                }
            });
        })(jQuery);


        (($)=> {
 
            /**
             * ADA Tabs
             */
            convertTableToTabsPanel($('table.tabs-panel'));
            replaceElements($('.tabs-panel > div > p > a'), 'button');
            replaceElements($('.tabs-panel > ul > li > a'), 'button');
            replaceElements($('.tabs-panel > div > a'), 'button');
            addTabButton($('.content-text'), 'button');
            removeDiv($('.tabs-panel'));

            const $tabContainers = $('.tabs-panel');

            const $allTabs = $(".tabs-panel > .tabs > .tab-title > button");
            const $allTablists = $(".tabs");
            const $allTabPanels = $(".tabs-content > .content");

            initializeTabs($allTablists, $allTabs, $allTabPanels);

            const usedTabIds = {};

            $tabContainers.each( (i, tabContainer) => {

                const $tabContainer = $(tabContainer);

                const $tabs = getTabsInContainer($tabContainer)
                const $panels = getPanelsInContainer($tabContainer);
                const $tablist = getTablistInContainer($tabContainer);

                if ($tabs.length ===0 || $panels.length === 0 || $tablist.length === 0
                    || $tabs.length !== $panels.length) {
                    console.log("### Unexpected tab container structure. Skipping to next.", $tabContainer);
                    return;
                }

                $tabs.first().attr('aria-selected', 'true');
                $tabs.first().addClass("active");
                $panels.first().addClass('active');

                initializeTabindexForTabset($tabs, $panels);

                $tabs.each( (i, tab) => {
                    const $tab = $(tab);
                    const $tabPanel = getTabContentPanel($tab);

                    const contentId = $tabPanel.attr('id');
                    const uniqueId = uniquifyId(contentId, usedTabIds);

                    const tabId = `tab-button-${uniqueId}`; // like tab-button-panel3-2
                    const panelId = `tab-${uniqueId}`;  // like tab-panel3-2

                    $tab.attr('id', tabId);
                    $tab.attr('aria-controls', panelId);

                    $tabPanel.attr('id', panelId);
                    $tabPanel.attr('aria-labelledby', tabId)
                });

                buildAccessibleTabs($tablist, $tabs, $panels)
            });

            addTablistAriaOwns($allTablists);
            addTablistDefaultAriaLabels($allTablists);
            uniquifyTablistAriaLabels($allTablists)

            removeUnneededAttributes($allTabs);

            function initializeTabs($tablists, $tabs, $tabpanels) {
                $tablists.attr('role', 'tablist');
                $tabs.attr('role', 'tab');
                $tabpanels.attr('role', 'tabpanel');

                $tabs.attr('aria-selected', 'false');

                $tabs.each(convertTabHrefToId);
            }

            function initializeTabindexForTabset($tabs, $panels) {
                $tabs.attr('tabindex', '-1');
                $tabs.first().removeAttr('tabindex');
                $panels.attr('tabindex', '0')
            }

            function convertTabHrefToId(i, tab) {
                const $tab = $(tab);
                const panelId = $tab.attr('href').replace(/^#/, '');
                $tab.attr('id', panelId);
                $tab.removeAttr('href');
            }

            /* given a tab button, return the associated content panel it controls
             * _before_ the tab button and panel ids have been uniquified across the page
             */
            function getTabContentPanel($tab) {
                const panelId = $tab.attr('id');debugger
                // return $tab.closest('.tabs').siblings('.tabs-content').children("#" + panelId);
                let $firstOption = $tab.closest('.tabs')
                    .children('.tab-title')
                    .siblings()
                    .children('.tabs-content')
                    .children("#" + panelId);

                let $secondOption = $tab.closest('.tabs')
                    .siblings('.tabs-content')
                    .children("#" + panelId);

                // Return the first option if it contains elements; otherwise, return the second option
                return $firstOption.length ? $firstOption : $secondOption;

            // // Return the first option if it contains elements; otherwise, return the second option
            // return $firstOption.length ? $firstOption : $secondOption;
            }

            function getTabsInContainer($tabContainer) {
                return $tabContainer.children("[role='tablist']").children(".tab-title").children("button[role='tab']");
            }

            function getPanelsInContainer($tabContainer) {
                // console.log("tabContainer > ul > li", $tabContainer.children("ul[role='tablist']").children("li"));
                // return $tabContainer.children("[role='tablist']").siblings('.tabs-content').children(".content");
                let $firstOption = $tabContainer.children("[role='tablist']")
                    .children('.tab-title')
                    .siblings()
                    .children('.tabs-content')
                    .children(".content");

                let $secondOption = $tabContainer.children("[role='tablist']")
                    .siblings('.tabs-content')
                    .children(".content");

                // Return the first option if it contains elements; otherwise, return the second option
                return $firstOption.length ? $firstOption : $secondOption;
            }

            function getTablistInContainer($tabContainer) {
                return $tabContainer.children("[role='tablist']");
            }

            function addTablistDefaultAriaLabels($tablists) {
                const defaultLabel = 'Tabbed Information';

                $tablists.not('[aria-label]').attr('aria-label', defaultLabel);
                $tablists.filter("[aria-label='']").attr('aria-label', defaultLabel);

                $tablists.filter("[aria-labelledby='']").removeAttr('aria-labelledby')
                // implicit: leave any non-empty string aria-labelledby values as-is
                // implicit: aria-labelledby overrules aria-label
            }

            function uniquifyTablistAriaLabels($tablists) {

                const usedTablistLabels = {}

                $tablists.each((i, tablist) => {
                    const ariaLabel = $(tablist).attr('aria-label');
                    const newLabel = uniquifyId(ariaLabel, usedTablistLabels, ' (', ') ', true);
                    $(tablist).attr('aria-label', newLabel);
                });
            }

            function addTablistAriaOwns(tablists) {
                /* Based on https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/tab_role

                   In our case, the element with role="tab" IS NOT a direct child of the element with role="tablist" (since
                   there is an intervening li wrapping the button). Thus, we must add an aria-owns attribute to the tablist
                   element, with a space-separated list of related element ids.

                   Alternatively, we could set role="tab" on the li instead, BUT the element with the tab role must ALSO
                   contain the aria-controls property, which we have already set on the button element.

                   Rather than move attributes or alter the DOM further, we chose to just build the list of tab element
                   ids and include it in the aria-owns property.
                 */

                $(tablists).each((i, tablist) => {
                    const childTabButtons = $(tablist).children('.tab-title').children('button');

                    const childTabButtonIds = $.map(childTabButtons, function(childTabButton, i) {
                        return $(childTabButton).attr('id');
                    });

                    $(tablist).attr('aria-owns', childTabButtonIds.join(' '));
                });
            }

            /**
             * ADA Accordion
             */
            convertTableToAccordionPanel($('table.accordion-panel'));
            replaceElements($('.accordion-navigation > a'), 'button');
            replaceElements($('.accordion-navigation'), 'div');
            replaceElements($('.accordion'), 'div');
            replaceElements($('.accordion-navigation > p'), 'button');
            removeDiv($('.accordion-panel'));

            const accordionContainers = $('.accordion');
            const accordionPanels = $('.accordion-navigation > .content');
            const accordionButtons = $('.accordion-navigation > button');

            const usedAccordionPanelIds = {}
            const usedAccordionContainerIds = {}

            $(accordionContainers).each((i, accordionContainer) => {
                const containerId = $(accordionContainer).attr('id');
                $(accordionContainer).attr('id', uniquifyId(containerId, usedAccordionContainerIds))
            });

            // Trim any text appended after link in tab/accordian title
            $('.accordion-navigation').each(function() {
                $(this).contents().filter(function() {
                    return this.nodeType === 3;
                }).remove();
            });

            $(accordionButtons).each((i, button) => {
                const accordionPanel = getAccordionContentPanel(button);

                let contentId = $(accordionPanel).attr('id');
                contentId = uniquifyId(contentId, usedAccordionPanelIds);

                $(button).attr('aria-controls', `accordion-${contentId}`);
                $(button).attr('id', `accordion-button-${contentId}`);
                $(button).attr('aria-expanded', 'false');
                $(button).removeAttr('tabindex'); //in some usecases accordion buttons were set to tabindex:-1, which doesn't allow user to reach accordions by tab.

                $(accordionPanel).attr('id', `accordion-${contentId}`);
                $(accordionPanel).attr('role', 'region');
                $(accordionPanel).attr('aria-labelledby', `accordion-button-${contentId}`);

                $(button).on('click', () => {
                    const currentAriaExpandedValue = $(button).attr('aria-expanded');

                    $(button).attr('aria-expanded', toggleAriaExpanded(currentAriaExpandedValue));
                    $(button).toggleClass('active');
                    $(accordionPanel).toggleClass('active');
                });

            });

            /**
             * ADA Topic folders list
             * Items should not be announced as list if there is only one tile item
             */
            const folderListTiles = $('.sub-page-topic-folders-list .topic-tile');

            if(folderListTiles.length === 1) {
                replaceElements($('.sub-page-topic-folders-list'), 'div');
                replaceElements($('.sub-page-topic-folders-list .topic-tile'), 'div');
            }

            insertAccordionIcons(accordionButtons);
            removeUnneededAttributes(accordionButtons);


            /* given an accordion button, return the associated content panel it controls */
            function getAccordionContentPanel(button) {
                return $(button).siblings(".content").first();
            }

            function insertAccordionIcons(buttons) {
                buttons.prepend('<span class="right-arrow" aria-hidden="true">&#x25B6;</span>');
                buttons.prepend('<span class="down-arrow" aria-hidden="true">&#x25BC;</span>');
            }

            function toggleAriaExpanded(currentValue) {
                if (currentValue === 'true') {
                    return "false";
                } else if (currentValue === 'false') {
                    return "true"
                } else {
                    return "undefined";
                }
            }

            function removeUnneededAttributes(elements) {
                /* tab and accordion button elements in the froala generated html have some attributes that we dont want */
                elements.removeAttr('data-tabindex-counter');    // not sure what this was for
                elements.removeAttr('data-tabindex-value');      // not sure what this was for
            }

            function uniquifyId(contentId, usedIds, $before="-", $after='', skipFirstOccurrenceSuffix = false) {

                if (contentId in usedIds) {
                    usedIds[contentId]++;
                } else {
                    usedIds[contentId] = 0;
                }

                if (usedIds[contentId] === 0 && skipFirstOccurrenceSuffix) {
                    return contentId;
                }

                const uniqueId = usedIds[contentId];

                return contentId += $before + usedIds[contentId].toString() + $after;

            }

            function replaceElements(elements, newElementName) {
                elements.wrap("<"+newElementName+">");

                elements.each(function(index) {
                    const element = $(this);
                    const parentElement = element.parent();

                    $.each(element.prop('attributes'), function() {
                        parentElement.attr(this.name, this.value);
                    });

                    element.contents().unwrap();

                });

                const newElements = elements.parent();
                return newElements;
            }

            function addTabButton($elements, newTag) {
                $elements.each(function () {
                    let $this = $(this);
                    let text = $this.text().trim(); // Get the text inside the element
                    let classList = $this.attr('class'); // Get the class attribute
                    let countMatch = classList.match(/\b\d+\b/); // Extract number from text
            
                    let buttonId = countMatch ? `#panel${countMatch[0]}` : ''; // Construct button ID if a number exists
            
                    let $newElement = $('<' + newTag + '/>', {
                        text: text, // Set button text
                        href: buttonId // Set button ID
                    });
            
                    if ($this.hasClass('content-text')) {
                        // If the <p> has class "content-text", clear it and append the button inside
                        $this.empty().append($newElement);
                    }
                });
            }

            function convertTableToTabsPanel($element) {
                // Check if the element *is* a table or *contains* a table
                let $table;
                if ($element.is("table")) {
                  $table = $element;
                } else if ($element.find("table").length > 0) {
                  $table = $element.find("table").first();
                } else {
                  // Exit early if no table is found
                  return;
                }
              
                const $rows = $table.find("tr");
                if ($rows.length < 2) return; // Ensure it has at least 2 rows
              
                const $tabHeaders = $rows.eq(0).find("td");
                const $tabContents = $rows.eq(1).find("td");
              
                const $tabsWrapper = $('<div>', {
                  class: 'tabs',
                  'data-tab': ''
                });
              
                const $tabsContentWrapper = $('<div>', {
                  class: 'tabs-content'
                });
              
                $tabHeaders.each(function (index) {
                  const $td = $(this);
                  const text = $td.text().trim();
                  const classList = $td.attr('class') || '';
                  const countMatch = classList.match(/\b\d+\b/);
                  const tabNumber = countMatch ? countMatch[0] : index + 1;
              
                  const $p = $('<p>', {
                    class: `tab-title content-text ${tabNumber}`,
                    text: text || `Tab ${tabNumber}`
                  });
              
                  $tabsWrapper.append($p);
                });
              
                $tabContents.each(function (index) {
                  const $td = $(this);
                  let html = $td.html().trim();
                  const id = $td.attr("id") || `panel${index + 1}`;
              
                  // Wrap in <p> if not starting with block-level tag
                  if (!/^<(p|div|ul|ol|table|blockquote|section|article|header|footer|h\d)/i.test(html)) {
                    html = `<p>${html}</p>`;
                  }
              
                  const $div = $('<div>', {
                    class: 'content',
                    id: id,
                    role: 'tabpanel',
                    tabindex: 0,
                    'aria-labelledby': `tab-button-${id}`,
                    html: html
                  });
              
                  const $spacer = $('<div>', {
                    class: 'spacer-class',
                    style: 'height: 1px;'
                  }).append('<br>');
              
                  $tabsContentWrapper.append($div).append($spacer);
                });
              
                const $tabsPanel = $('<div>', {
                  class: 'tabs-panel'
                }).append($tabsWrapper)
                  .append('<div class="spacer-class" style="height: 1px;"><br></div>')
                  .append($tabsContentWrapper);
              
                $table.replaceWith($tabsPanel);
              }

              function convertTableToAccordionPanel($element) {
                // Check if the element *is* a table or *contains* a table
                let $table;
                if ($element.is("table")) {
                  $table = $element;
                } else if ($element.find("table.accordion-panel").length > 0) {
                  $table = $element.find("table.accordion-panel").first();
                } else {
                  return;
                }
              
                const $rows = $table.find("tr");
                if ($rows.length < 2) return;
              
                const $accordionWrapper = $('<div>', {
                  class: 'accordion-panel'
                });
              
                for (let i = 0; i < $rows.length; i += 2) {
                  const $headerRow = $rows.eq(i);
                  const $contentRow = $rows.eq(i + 1);
              
                  const $headerCell = $headerRow.find("td").first();
                  const $contentCell = $contentRow.find("td").first();
              
                  const headerText = $headerCell.text().trim();
                  let contentHtml = $contentCell.html().trim();
                  const panelId = ($contentCell.attr("id") || `panel${(i / 2) + 1}`).replace(/\s+/g, '');
              
                  // Wrap content in <p> if not block-level
                  if (!/^<(p|div|ul|ol|table|blockquote|section|article|header|footer|h\d)/i.test(contentHtml)) {
                    contentHtml = `<p>${contentHtml}</p>`;
                  }
              
                  const $accordionNavigation = $('<div>', {
                    class: 'accordion-navigation'
                  });
              
                  const $headerP = $('<p>', {
                    class: 'accordion-header',
                    href: `#${panelId}`,
                    text: headerText || `Accordion ${(i / 2) + 1}`
                  });
              
                  const $headerSpacer = $('<div>', {
                    class: 'spacer-class',
                    style: 'height: 1px;'
                  }).append('<br>');
              
                  const $contentDiv = $('<div>', {
                    class: 'content',
                    id: panelId,
                    html: contentHtml
                  });
              
                  const $contentSpacer = $('<div>', {
                    class: 'spacer-class',
                    style: 'height: 1px;'
                  }).append('<br>');
              
                  $contentDiv.append($contentSpacer);
              
                  $accordionNavigation
                    .append($headerP)
                    .append($headerSpacer)
                    .append($contentDiv);
              
                  const $accordion = $('<div>', {
                    class: 'accordion',
                    'data-accordion': ''
                  }).append($accordionNavigation);
              
                  $accordionWrapper.append($accordion);
                }
              
                $table.replaceWith($accordionWrapper);
              }

            function removeDiv(container) {
                let elements = container.find('div.spacer-class');
                if (elements.length > 0) {
                    elements.remove();
                }
            }

            // /*
            //  * ADA Duplicate Embedded Article Ids
            //  */

            const usedEmbeddedArticleIds = {};
            const embeddedArticles = $('article');

            $(embeddedArticles).each((i, embeddedArticle) => {

                const embeddedArticleId = $(embeddedArticle).attr('id');
                const uniqueEmbeddedArticleId = uniquifyId(embeddedArticleId, usedEmbeddedArticleIds);

                $(embeddedArticle).attr('id', uniqueEmbeddedArticleId);

                // adjust aria-controls attribute on the related disclosure button
                const disclosureButton = $(embeddedArticle).children('header').children('button.openCloseButton');
                if ( disclosureButton && $(disclosureButton).attr('aria-controls') === embeddedArticleId) {
                    $(disclosureButton).attr('aria-controls', uniqueEmbeddedArticleId);
                }

            });


        })(jQuery);

        /**
         * ADA Mobile Header
         */
        (($) => {
            const loginButton = $('ul.user-controls button.login'),
                logoutButton = $('ul.user-controls button.logout'),
                loginButtonMobile = $('ul.title-area button.login-mobile'),
                logoutButtonMobile = $('ul.title-area button.logout-mobile'),
                navigationButtons = $('ul.user-controls li button'),
                hamburgerMenu = $('li button.mobile-toggle'),
                linksLength = $('li button.header-link.links').length;


            /**
             * Set default 'aria-expanded' value
             */
            $('nav, button.mobile-toggle').attr('aria-expanded', 'false');

            $('button.mobile-toggle').click((e) => {
                if($('nav, button.mobile-toggle').attr('aria-expanded') === 'true') {
                    $('nav, button.mobile-toggle').attr('aria-expanded', 'false');
                } else {
                    $('nav, button.mobile-toggle').attr('aria-expanded', 'true');
                }
            });

            $('#nav-search-link').click((e)=>{
                const bannerContainerIsVisable= $('#bannerContainer').is(':visible');

                if($('nav, button.mobile-toggle').attr('aria-expanded') === 'true' && bannerContainerIsVisable === false) {
                    $('nav, button.mobile-toggle').attr('aria-expanded', 'false');
                } else {
                    $('nav, button.mobile-toggle').attr('aria-expanded', 'true');
                }
            });

            $('.search-close-icon').click((e) => {
                $('nav, button.mobile-toggle, #bannerContainer').attr('aria-expanded', 'false');
            });

            if(!linksLength) {
                hamburgerMenu.addClass('has-no-links')
            } else if(linksLength && hamburgerMenu.hasClass('has-no-links')) {
                hamburgerMenu.removeClass('has-no-links');
            }

            const loginUrl = loginButton.attr('href');
            loginButton.click(e => {
                window.location = loginUrl;
            });

            logoutButton.click(e => {
                e.preventDefault();

                if (port) {
                    port.postMessage({action: 'logout'});
                }
                else {
                    logoutUser();
                }
            });

            if(logoutButton.attr('title') && logoutButton.attr('title').length !== 0) {
                loginButton.hide();
                logoutButton.show();
            } else {
                logoutButton.hide();
                loginButton.show();
            }

            //Onclick action for login and logout buttons in mobile view
            const loginUrlMobile = loginButtonMobile.attr('href');
            loginButtonMobile.click(e => {
                window.location = loginUrlMobile;
            });

            logoutButtonMobile.click(e => {
                e.preventDefault();

                if (port) {
                    port.postMessage({action: 'logout'});
                }
                else {
                    logoutUser();
                }
            });

            if(logoutButtonMobile.attr('title') && logoutButtonMobile.attr('title').length !== 0) {
                loginButtonMobile.hide();
                logoutButtonMobile.show();
            } else {
                logoutButtonMobile.hide();
                loginButtonMobile.show();
            }

            const first = $('ul.user-controls li button')[0];
            const lastButton = $('ul.user-controls li button')[navigationButtons.length - 1];

            function isMobile() {
                return $('button.mobile-toggle').is(':visible');
            }

            $(first).on('keydown', (e) => {
                const keyCode = e.keyCode;

                if(keyCode === 9 && e.shiftKey && isMobile()) {
                    e.preventDefault();

                    $(lastButton).focus();
                }
            });

            $(lastButton).on('keydown', (e) => {
                const keyCode = e.keyCode;
                const tabButtonCode = 9;

                if(keyCode === tabButtonCode && isMobile()) {
                    if(e.shiftKey) {
                        return;
                    } else {
                        e.preventDefault();

                        $('ul.user-controls li button')[0].focus();
                    }
                }
            });

            $('ul.user-controls').on('keydown', (e) => {
                const keyCode = e.keyCode;
                const escButtonCode = 27;

                if(keyCode === escButtonCode) {
                    $('.top-bar').removeClass('expanded');

                    $('nav, button.mobile-toggle').attr('aria-expanded', 'false');

                    if(isMobile()) {
                        $('button.mobile-toggle').focus();
                    } else {
                        $('main').find('button, a.breadcrumb, input').first().trigger('hover').focus();
                    }
                }
            });
        })(jQuery);
    });


    /**
     * @param $tablist - jQuery object for the DOM element with role="tablist"
     * @param $tabs    - jQuery object collection for the tab buttons with role="tab"
     * @param $panels  - jQuery object collection for the tab panels with role="tabpanel"
     */
    function buildAccessibleTabs($tablist, $tabs, $panels) {

        /*
         *   An adaptation of Accessible Tabs functionality described by
         *   https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-1/tabs.html
         *
         *   This software or document includes material copied from or derived from
         *   https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-1/js/tabs.js
         * . Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
         *
         *   which is licensed according to the W3C Software License at
         *   https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
         */

        // this function adapted from w3.org reference code for ADA tabs:
        // https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-1/tabs.html

        // convert jQuery collections passed as arguments to plain DOM element arrays
        // needed
        const tablist = $tablist[0];
        const tabs = $tabs.get();
        const panels = $panels.get();

        const delay = determineDelay();

        // For easy reference
        const keys = {
            end: 35,
            home: 36,
            left: 37,
            up: 38,
            right: 39,
            down: 40,
            delete: 46,
        };

        // Add or subtract depending on key pressed
        const direction = {
            37: -1,
            38: -1,
            39: 1,
            40: 1,
        };

        for (let i = 0; i < tabs.length; ++i) {
            addListeners(i);
        }

        function addListeners(index) {
            tabs[index].addEventListener('click', clickEventListener);
            tabs[index].addEventListener('keydown', keydownEventListener);
            tabs[index].addEventListener('keyup', keyupEventListener);

            // Build an array with all tabs (<button>s) in it
            tabs[index].index = index;
        }

        // When a tab is clicked, activateTab is fired to activate it
        function clickEventListener(event) {
            let tab = event.target;
            activateTab(tab, false);
        }

        // Handle keydown on tabs
        function keydownEventListener(event) {
            const key = event.keyCode;

            switch (key) {
                case keys.end:
                    event.preventDefault();
                    // Activate last tab
                    activateTab(tabs[tabs.length - 1]);
                    break;
                case keys.home:
                    event.preventDefault();
                    // Activate first tab
                    activateTab(tabs[0]);
                    break;

                // Up and down are in keydown
                // because we need to prevent page scroll >:)
                case keys.up:
                case keys.down:
                    determineOrientation(event);
                    break;
            }
        }

        // Handle keyup on tabs
        function keyupEventListener(event) {
            const key = event.keyCode;

            switch (key) {
                case keys.left:
                case keys.right:
                    determineOrientation(event);
                    break;
            }
        }

        // When a tablistâ€™s aria-orientation is set to vertical,
        // only up and down arrow should function.
        // In all other cases only left and right arrow function.
        function determineOrientation(event) {
            const key = event.keyCode;
            const vertical = tablist.getAttribute('aria-orientation') == 'vertical';
            let proceed = false;

            if (vertical) {
                if (key === keys.up || key === keys.down) {
                    event.preventDefault();
                    proceed = true;
                }
            } else {
                if (key === keys.left || key === keys.right) {
                    proceed = true;
                }
            }

            if (proceed) {
                switchTabOnArrowPress(event);
            }
        }

        // Either focus the next, previous, first, or last tab
        // depending on key pressed
        function switchTabOnArrowPress(event) {
            const pressed = event.keyCode;

            $(tabs).focus(focusEventHandler);

            if (direction[pressed]) {
                const target = event.target;
                if (target.index !== undefined) {
                    if (tabs[target.index + direction[pressed]]) {
                        tabs[target.index + direction[pressed]].focus();
                    } else if (pressed === keys.left || pressed === keys.up) {
                        focusLastTab();
                    } else if (pressed === keys.right || pressed === keys.down) {
                        focusFirstTab();
                    }
                }
            }
        }

        // Activates any given tab panel
        function activateTab(tab, setFocus) {

            const $tab = $(tab);

            setFocus = setFocus || true;

            // Deactivate all other tabs
            deactivateTabs();

            // Remove tabindex attribute
            $tab.removeAttr('tabindex');

            // Set the tab as selected
            $tab.attr('aria-selected', 'true');
            $tab.addClass('active');

            // Get the value of aria-controls (which is an ID)
            const controls = $tab.attr('aria-controls');

            const $panel = $("#" + controls);
            $panel.addClass("active");

            // Set focus when required
            if (setFocus) {
                tab.focus();
            }
        }

        // Deactivate all tabs and tab panels
        function deactivateTabs() {
            const $tabs = $(tabs);
            $tabs.attr('tabindex', '-1');
            $tabs.attr('aria-selected', 'false');
            $tabs.off('focus', focusEventHandler);
            $tabs.removeClass('active');

            const $panels = getTabPanels($tabs);
            $panels.removeClass('active');
        }

        function getTabPanels($tabs) {
            let $firstOption = $tabs.closest('.tabs')
                .children('.tab-title')
                .siblings()
                .children('.tabs-content')
                .children(".content");
        
            let $secondOption = $tabs.closest(".tabs")
                .siblings('.tabs-content')
                .children('.content');
        
            // Return the first option if it contains elements; otherwise, return the second option
            return $firstOption.length ? $firstOption : $secondOption;
        }

        // Make a guess
        function focusFirstTab() {
            tabs[0].focus();
        }

        // Make a guess
        function focusLastTab() {
            tabs[tabs.length - 1].focus();
        }

        // Determine whether there should be a delay
        // when user navigates with the arrow keys
        function determineDelay() {
            const hasDelay = tablist.hasAttribute('data-delay');
            let delay = 0;

            if (hasDelay) {
                var delayValue = tablist.getAttribute('data-delay');
                if (delayValue) {
                    delay = delayValue;
                } else {
                    // If no value is specified, default to 300ms
                    delay = 300;
                }
            }

            return delay;
        }

        //
        function focusEventHandler(event) {
            const target = event.target;

            setTimeout(checkTabFocus, delay, target);
        }

        // Only activate tab on focus if it still has focus after the delay
        function checkTabFocus(target) {
            const focused = document.activeElement;

            if (target === focused) {
                activateTab(target, false);
            }
        }
    }

    function logoutUser(){
        $.post(window.href, {logout: 1}).done(function (response) {
            if (response.success) {
                location.reload();
            }
        });
    }

    window.onload = function() {
        var reloading = sessionStorage.getItem("reloading");
        if (reloading) {
            sessionStorage.removeItem("reloading");
            port.postMessage({action: 'showLoginModal'});
            showLoginModal();
        }
    }

    function showLoginModal(){
        //Show logout modal
        if(!logoutModal){
            logoutModal = new ConfirmDialog({
                width: '420px',
                height: '220px',
                ui: 'wc-alert',
                title: 'You\'ve Been Logged Out',
                cancelButtonText: 'Close',
                okButtonText: 'Login',
                iconCls: 'wc-warn-icon',
                closeable: true,
                closeBtnAriaText: 'Close',
                message: 'You\'ve been logged out due to inactivity.<div>To continue accessing internal Articles, Please log in again.</div>',
                callback: function(id){
                    if (id === 'ok') {
                        loginUsers();
                        port.postMessage({action: 'login'});
                    }
                }
            });
        }
    }

    function loginUsers() {
        var loginBtn = document.getElementsByClassName('login');

        if(loginBtn.length){
            window.location.replace(document.getElementsByClassName('login')[0].getAttribute('href'));
        }
    }

    function showPopup(){
        sessionTimer = new ConfirmDialog({
            width: '500px',
            height: '280px',
            title: 'Session Timeout Warning',
            cancelButtonText: 'Logout',
            okButtonText: 'Continue Session',
            iconCls: 'wc-warn-icon',
            closeable: true,
            closeBtnAriaText: 'closing this modal will continue your session',
            message: 'Warning. Your session is expiring due to inactivity.<div class="wc-dialog-body-cont"><b>If you want to continue working, click the "Continue Session" button.</b></div>Your session will expire in <span id="session-count"><b></b></span> Seconds',
            callback: function(id){
                clearInterval(timer);
                if (id === 'ok' || id === 'close') {
                    port.postMessage({action: 'start', closeAllModals: true});
                }
                else if (id === 'cancel'){
                    port.postMessage({action: 'logout'});
                }
            }
        });
        countDown();
    }

    function countDown(){
        let seconds = 60;

        timer = setInterval(function() {
            if(document.getElementById("session-count")){
                document.getElementById("session-count").innerHTML = seconds;
            }

            seconds--;

            // If the count down is over, clear the interval
            if (seconds === 0) {
                clearInterval(timer);

                //close the session timeout window.
                sessionTimer.hide();

                //logout the user
                logoutUser();
                sessionStorage.setItem("reloading", "true");
                port.postMessage({action: 'logout'});
            }
        }, 1000);
    }

    function linkSearchButtons() {
        const searchElement = document.getElementById('search');
        if(searchElement != null) {
            const searchLinkMobile = document.getElementById('nav-search-link-mobile');
            if(searchLinkMobile != null) {
                searchLinkMobile.addEventListener('click', (e) => {
                    e.preventDefault();
                    searchElement.select();
                });
            }
            const navSearchLink = document.getElementById('nav-search-link');
            if(navSearchLink != null) {
                navSearchLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    searchElement.select();
                });
            }
        }
    }
    linkSearchButtons();
})();

      </script>
      `;

      // Create title section HTML
      const titleSection = `
        <div class="title-section">
          <div class="title-field">
            <h1>${this.title || ''}</h1>
          </div>
          ${this.subtitle ? `
          <div class="subtitle-field">
            <h2>${this.subtitle}</h2>
          </div>
          ` : ''}
          <div class="article-meta">
            ${this.updated ? `
            <span class="updated">
              Updated ${this.updated}
            </span>
            ` : ''}
          </div>
        </div>
      `;

      const html = `
      <!DOCTYPE html>
      <html>
        <head>
          ${styles}
          ${scripts}
        </head>
        <body>
          ${titleSection}
          ${this.content || ''}
        </body>
      </html>
      `;

      if (doc) {
        doc.open();
        doc.writeln(html);
        doc.close();
      }
    }
  },
  watch: {
    // Re-render when content or CSS changes
    content() {
      this.renderContent();
    },
    libraryCss() {
      this.renderContent();
    },
    title() {
      this.renderContent();
    },
    subtitle() {
      this.renderContent();
    },
    updated() {
      this.renderContent();
    },
    visibility() {
      this.renderContent();
    }
  }
};
</script>

<style scoped>
  .article-preview {
    flex: 1;
    width: 100%;
  }
</style>

